# Kompletní analýza setů CTEC pro taktování výroby V2.1

## P<PERSON><PERSON>led
Tento pokročilý Python script analyzuje Excel soubory s daty o kabelových sestavách a vytváří komplexní analýzy pro optimalizaci taktování výroby. Script extrahuje maximum informací z vašich dat, mapuje komponenty na pracoviště a poskytuje detailní doporučení pro organizaci výrobních procesů.

## 🆕 Klíčové funkce verze 2.1

### 🔧 Přesná definice složitosti terminací
- **Simple**: Ferrules 0.5-10mm² (0.2 min, standardní krimpovací nástroje)
- **Complex**: Všechny ostatní terminace 0.5-25mm² (0.4 min, pokročilé nástroje)
- **Ultra Complex**: Všechny terminace >25mm² (1.2 min, hydraulický press)

### 🏭 Integrace s pracovišti
- Načítá data ze souboru `workstations.xlsx`
- Mapuje komponenty na konkrétní pracoviště (A1, A2, A3, B1, B2, C1, D1)
- Přidává informace o strojích a dostupnosti
- Vypočítává časy přípravy pracovišť

### 🔪 Oddělení Cutting vs Pracoviště
**CUTTING - SOLO ANALÝZA** (kabely přicházejí nastříhané):
- Samostatná operace před výrobou
- Celkový čas: 70.4 hodin (22% celkového času)
- Rychlost: 50 m/hodinu
- Průměrný čas: 1.58 min/kabel

**PRACOVIŠTĚ - VÝROBNÍ ANALÝZA**:
- Čas bez cutting: 244.4 hodin (78% celkového času)
- Průměrný čas: 5.48 min/kabel
- Poměr pracoviště/cutting: 3.5:1

## Automatické zpracování Excel souborů
- Podporuje formáty .xlsx a .xlsm
- Automaticky detekuje záhlaví v různých pozicích
- Robustní parser pro různé formáty dat
- Zpracovává všechny soubory v aktuální složce (kromě workstations.xlsx)

## Rozšířená extrakce dat
Script extrahuje následující informace:
- **Základní identifikátory**: Cable ID, Bundle ID, pořadí výroby
- **Specifikace kabelů**: průřez, barva, délka, kód kabelu
- **Komponenty**: PN čísla, popisy, délky odizolování (A i B strana)
- **Terminace**: typy, přesná složitost podle nových pravidel
- **Pracoviště**: mapování komponentů na pracoviště, stroje, dostupnost
- **Lokace**: instalace, zařízení, piny
- **Metadata**: zdrojový soubor, typ souboru

## Pokročilé odhady výrobních časů
Automaticky vypočítává:
- **Cutting čas** (solo): 0.0012 min/mm
- **Čas odizolování**: na základě délek strip
- **Čas terminace** podle přesné složitosti:
  * Simple: 0.2 min
  * Complex: 0.4 min  
  * Ultra Complex: 1.2 min
- **Čas přípravy pracovišť**: 1.5-3.0 min podle typu
- **Celkový čas na pracovišti** (bez cutting)
- **Čas kontroly kvality**: 0.2 min

## Pokročilé analýzy

### Bundle Analysis
- Počet kabelů v bundlu
- Celkový a průměrný výrobní čas (bez cutting)
- Rozsah průřezů
- Počet složitých terminací
- Skóre složitosti bundlu

### Cross Section Analysis
- Analýza podle průřezů kabelů
- Celkové délky a časy výroby
- Počet bundlů pro každý průřez

### Component Analysis
- Nejpoužívanější komponenty
- Frekvence použití
- Složitost terminací
- Mapování na pracoviště

### Workstation Analysis
- Rozdělení podle složitosti terminací (Simple/Complex/Ultra Complex)
- Analýza podle skutečných pracovišť ze souboru workstations.xlsx
- Doporučené velikosti dávek pro různé typy pracovišť

### 🆕 Cutting Analysis SOLO
- Celková cutting statistika
- Analýza podle délek kabelů (5 kategorií)
- Top bundly pro cutting operace
- Doporučené velikosti dávek pro cutting

### Takt Time Analysis
- Doporučené taktovací časy s 20% bufferem
- Odhad času přípravy
- Kategorizace složitosti (Low/Medium/High)
- Doporučené velikosti dávek
- Čas na dávku

### Daily Production Plan
- Denní plán výroby (8 hodin, 85% efektivita)
- Prioritizace bundlů podle složitosti a objemu
- Využití kapacity
- Sekvencování výroby

## Výstupní soubory

### Excel soubor (Kompletni_analyza_pro_taktovani.xlsx)
Obsahuje následující listy:

1. **Complete_Data** - Všechna zpracovaná data (42 sloupců)
2. **Summary_Statistics** - Souhrnné statistiky
3. **Production_Recommendations** - Doporučení pro výrobu
4. **Workstation_Analysis** - Analýza pracovišť
5. **🆕 Cutting_Analysis_SOLO** - Solo cutting analýza
6. **Takt_Time_Analysis** - Taktovací analýza
7. **Daily_Production_Plan** - Denní plán výroby
8. **Bundle_Priority** - Priorita bundlů
9. **Bundle_Analysis** - Detailní analýza bundlů
10. **Cross_Section_Analysis** - Analýza průřezů
11. **Component_Analysis** - Analýza komponentů
12. **Length_Analysis** - Analýza délek

### CSV soubor (Kompletni_analyza_pro_taktovani.csv)
- Všechna data v jednom souboru pro další zpracování
- UTF-8 encoding pro správné zobrazení českých znaků

## Klíčové metriky

### Souhrnné statistiky (z posledního běhu V2.1):
- **Celkem kabelů**: 2,674
- **Unikátních bundlů**: 46
- **Celková délka kabelů**: 3,520.75 m
- **Celkový čas na pracovištích**: 244.4 hodin (bez cutting)
- **Celkový čas cutting**: 70.4 hodin (solo)
- **Celkový čas včetně cutting**: 314.8 hodin
- **Průměrný čas na pracovišti**: 5.48 minut/kabel
- **Průměrný čas cutting**: 1.58 minut/kabel
- **Složité terminace**: 433 kusů
- **Ultra složité terminace**: 45 kusů (>25mm²)
- **Rozsah průřezů**: 0.5-120.0 mm²
- **Mapovaných komponentů na pracoviště**: 4,978

### Statistiky složitosti terminací:
- **Simple + Simple**: 2,196 kabelů (82%)
- **Complex + Complex**: 215 kabelů (8%)
- **Complex + Simple**: 142 kabelů (5%)
- **Simple + Complex**: 76 kabelů (3%)
- **Ultra Complex + Ultra Complex**: 45 kabelů (2%)

### Rozdělení podle pracovišť:
- **Pracoviště A1**: 1,665 komponentů (ferrules, jednoduché terminace)
- **Pracoviště C1**: 347 komponentů (twin sleeves, speciální ferrules)
- **Pracoviště A3**: 274 komponentů (cable lugs, rings)
- **Pracoviště A2**: 146 komponentů (střední ferrules)
- **Pracoviště B2**: 62 komponentů (hydraulický press, velké průřezy)

### Cutting analýza podle délek:
- **Very Short (0-300mm)**: 396 kabelů, 0.26 min/kabel, 100-200 kusů/dávka
- **Short (300-600mm)**: 425 kabelů, 0.57 min/kabel, 80-150 kusů/dávka
- **Medium (600-1000mm)**: 481 kabelů, 0.96 min/kabel, 60-120 kusů/dávka
- **Long (1000-2000mm)**: 791 kabelů, 1.82 min/kabel, 40-80 kusů/dávka
- **Very Long (2000mm+)**: 581 kabelů, 3.42 min/kabel, 20-50 kusů/dávka

## Doporučení pro taktování podle nových pravidel

### Cutting operace (předvýroba):
- **Kapacita**: 490 m/den (8 hodin, 85% efektivita)
- **Rychlost**: 50 m/hodinu konstantní
- **Plánování**: Podle celkové délky bundlů
- **Dávkování**: 20-200 kusů podle délek kabelů

### Výrobní pracoviště:
1. **Simple terminace (ferrules 0.5-10mm²)**: 30-60 kabelů na dávku
2. **Complex terminace (lugs, rings 0.5-25mm²)**: 15-30 kabelů na dávku
3. **Ultra Complex terminace (>25mm²)**: 5-15 kabelů na dávku
4. **Pracoviště A1**: Vysokoobjemová výroba, velké dávky
5. **Pracoviště B2**: Malé dávky, speciální příprava

### Sekvencování výroby:
1. **Cutting** - Příprava kabelů podle bundlů (batch operace)
2. **Pracoviště A1** - Simple terminace (vysoký objem)
3. **Pracoviště A3** - Complex terminace
4. **Pracoviště B2** - Ultra complex terminace

## Kapacitní plánování

### Cutting pracoviště:
- **Denní kapacita**: 490 m kabelů
- **Týdenní kapacita**: 2,450 m kabelů
- **Měsíční kapacita**: 10,780 m kabelů

### Výrobní pracoviště:
- **A1 (Simple)**: 80-120 kabelů/den
- **A3 (Complex)**: 40-60 kabelů/den  
- **B2 (Ultra Complex)**: 15-25 kabelů/den

### Týdenní plán:
1. **Pondělí**: Cutting pro celý týden (batch příprava)
2. **Úterý-Pátek**: Výroba na pracovištích podle priority
3. **Pátek odpoledne**: Dokončení a kontrola kvality

## Použití

1. Umístěte všechny Excel soubory do stejné složky jako script
2. Ujistěte se, že máte soubor `workstations.xlsx` s listy:
   - `Components_Workstations` (mapování komponentů)
   - `Workstations_Definition` (definice pracovišť)
3. Spusťte: `python "#Analýza setů Ctec.py"`
4. Script automaticky zpracuje všechny .xlsx a .xlsm soubory
5. Výsledky budou uloženy do Excel a CSV souborů

## Technické požadavky

```python
pip install pandas openpyxl numpy
```

## Konfigurace

Script obsahuje nastavitelné parametry v sekci TIME_ESTIMATES:
- **cutting_per_mm**: 0.0012 min (solo cutting)
- **crimping_simple**: 0.2 min (ferrules 0.5-10mm²)
- **crimping_complex**: 0.4 min (lugs, rings 0.5-25mm²)
- **crimping_ultra_complex**: 1.2 min (hydraulický press >25mm²)
- **bundling_per_cable**: 0.1 min
- **quality_check**: 0.2 min
- **setup_workstation_A/B/C**: 1.5-3.0 min

## Výhody pro taktování výroby

1. **Oddělené plánování** - Cutting vs výrobní pracoviště
2. **Přesné odhady časů** - Na základě skutečných parametrů
3. **Optimalizace dávek** - Podle složitosti a pracovišť
4. **Plánování kapacity** - Denní plány s využitím kapacity
5. **Prioritizace** - Seřazení bundlů podle složitosti a objemu
6. **Rozdělení práce** - Mapování na konkrétní pracoviště
7. **Sledování efektivity** - Metriky pro kontinuální zlepšování

Script poskytuje kompletní základ pro implementaci taktovaného výrobního systému s jasným oddělením cutting operací od hlavní výroby na pracovištích.

## Nové sloupce v Complete_Data

### Základní identifikace:
- `Cable_ID`, `Bundle_ID`, `Production_Order`
- `Cable_PN`, `Cable_Code`, `Cross_Section_mm2`
- `Wire_Color`, `Cable_Length_mm`

### Komponenty a terminace:
- `Component_A_PN`, `Component_A_Description`, `Component_A_Strip_Length`
- `Component_B_PN`, `Component_B_Description`, `Component_B_Strip_Length`
- `Termination_A_Complexity`, `Termination_B_Complexity` (simple/complex/ultra_complex)

### 🆕 Pracoviště a stroje:
- `Component_A_Workstation`, `Component_B_Workstation`
- `Workstation_A_Machines`, `Workstation_B_Machines`
- `Workstation_A_Available`, `Workstation_B_Available`

### 🆕 Časy (oddělené):
- `Workstation_Production_Time_Min` - Čas na pracovišti (BEZ cutting)
- `Cutting_Time_Min` - Solo cutting čas
- `Total_Time_Including_Cutting_Min` - Celkový čas
- `Stripping_Time_Min`, `Termination_Time_Min`, `Setup_Time_Min`

### Lokace a instrukce:
- `Installation_A`, `Installation_B`, `Location_A`, `Location_B`
- `Device_A`, `Device_B`, `Pin_A`, `Pin_B`
- `Special_Instructions`

### Metadata:
- `Source_File`, `File_Type`

## Metriky pro sledování efektivity

### Cutting metriky:
- **m/hodinu** - rychlost cutting (cíl: 50 m/h)
- **% využití** - efektivita cutting pracoviště (cíl: 85%)
- **Přesnost délek** - kvalita cutting (cíl: ±1mm)

### Pracoviště metriky:
- **min/kabel** - rychlost výroby podle složitosti
- **% využití** - efektivita pracovišť (cíl: 85%)
- **Kvalita terminací** - defekty na 1000 kusů (cíl: <5)
- **Setup čas** - čas přípravy mezi dávkami (cíl: <3 min)

### Celkové metriky:
- **OEE** - Overall Equipment Effectiveness
- **Takt time adherence** - dodržování taktovacích časů
- **First pass yield** - kvalita na první pokus

## Troubleshooting

### Časté problémy:
1. **"Cable-No. nenalezen"** - Zkontrolujte formát Excel souboru
2. **"Pracoviště nenačtena"** - Ověřte soubor workstations.xlsx
3. **"Nulové časy"** - Zkontrolujte TIME_ESTIMATES hodnoty

### Řešení:
- Ujistěte se, že Excel soubory mají správné záhlaví
- Workstations.xlsx musí obsahovat oba listy
- Všechny soubory musí být ve stejné složce jako script

Tento kompletní systém poskytuje vše potřebné pro efektivní taktování výroby kabelových sestav.
