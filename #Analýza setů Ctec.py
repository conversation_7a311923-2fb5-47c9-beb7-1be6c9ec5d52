# Rozšířená analýza setů Ctec pro taktování výroby
# Verze 2.0 - Komplexní extrakce dat pro optimalizaci výrobních procesů

import pandas as pd
import os
import glob
import re
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# --- NASTAVENÍ (UPRAVTE PODLE POTŘEBY) ---

# Cesta ke složce s vašimi Excel soubory - aktuální složka
cesta_k_souborum = '.'

# Názvy výstupních souborů
vystupni_soubor_excel = 'Kompletni_analyza_pro_taktovani.xlsx'
vystupni_soubor_csv = 'Kompletni_analyza_pro_taktovani.csv'

# Název klíčového sloupce pro identifikaci začátku dat
start_column_name = 'Cable-No.'

# Rozšířené mapování sloupců - pokrývá všechny varianty názvů
COLUMN_MAPPINGS = {
    # Základní identifikátory
    'Cable-No.': 'Cable_ID',
    'Bundle-No.': 'Bundle_ID',
    'Bundle': 'Bundle_ID',
    'Pořadí pro výrobu': 'Production_Order',
    'Ranking for production': 'Production_Order',

    # Specifikace kabelu
    'Cable PN': 'Cable_PN',
    'Cable Code': 'Cable_Code',
    'Cable Section': 'Cross_Section_mm2',
    'CSA': 'Cross_Section_mm2',
    'Colour': 'Wire_Color',
    'Color': 'Wire_Color',
    'Length': 'Cable_Length_mm',

    # Komponenty A (source)
    'Component PN - A': 'Component_A_PN',
    'Component PN - A\n': 'Component_A_PN',
    'Description': 'Component_A_Description',
    'Description\n': 'Component_A_Description',
    'Description A': 'Component_A_Description',
    'Odizol': 'Component_A_Strip_Length',
    'odizol': 'Component_A_Strip_Length',

    # Komponenty B (target)
    'Component PN - B': 'Component_B_PN',
    'Component PN - B\n': 'Component_B_PN',
    'Description.1': 'Component_B_Description',
    'Description\n.1': 'Component_B_Description',
    'Description B': 'Component_B_Description',
    'Odizol.1': 'Component_B_Strip_Length',
    'odizol.1': 'Component_B_Strip_Length',

    # Terminace a zpracování
    'Termination': 'Termination_Type',
    '\nTermination\n': 'Termination_A_Type',
    'Terminat.\nRotation': 'Termination_Rotation',
    'Isolation / Protection': 'Isolation_Protection_A',
    'Isolation / Protection.1': 'Isolation_Protection_B',

    # Lokace a instalace
    'Installation': 'Installation_A',
    'Installation.1': 'Installation_B',
    'Location': 'Location_A',
    'Location.1': 'Location_B',
    'Device': 'Device_A',
    'Device.1': 'Device_B',
    'Pin': 'Pin_A',
    'Pin.1': 'Pin_B',

    # Instrukce
    'Instruction': 'Special_Instructions'
}

# Odhady časů pro různé operace (v minutách)
TIME_ESTIMATES = {
    'base_handling': 0.5,  # Základní manipulace s kabelem
    'cutting_per_mm': 0.0012,  # Čas řezání na mm
    'stripping_per_mm': 0.1,  # Čas odizolování na mm
    'crimping_simple': 0.2,  # Jednoduchá terminace (ferrules 0.5-10mm²)
    'crimping_complex': 0.4,  # Složitá terminace (lugs, rings 0.5-25mm²)
    'crimping_ultra_complex': 1.2,  # Ultra složitá terminace (hydraulický press >25mm²)
    'bundling_per_cable': 0.1,  # Bundlování na kabel
    'quality_check': 0.2,  # Kontrola kvality
    'setup_workstation_A': 2.0,  # Čas přípravy pracoviště A
    'setup_workstation_B': 3.0,  # Čas přípravy pracoviště B
    'setup_workstation_C': 1.5,  # Čas přípravy pracoviště C
}

# --- KONEC NASTAVENÍ ---


def find_header_row(filepath, marker_text):
    """Najde index řádku, který obsahuje text ze 'marker_text'."""
    try:
        df_probe = pd.read_excel(filepath, header=None, nrows=25)
        for index, row in df_probe.iterrows():
            if any(str(cell).strip() == marker_text for cell in row):
                return index
    except Exception as e:
        print(f"  - Varování: Nepodařilo se prohledat soubor {os.path.basename(filepath)} pro záhlaví: {e}")
    return None

def extract_numeric_value(value, default=None):
    """Extrahuje číselnou hodnotu z textu."""
    if pd.isna(value) or value is None:
        return default
    try:
        s_val = str(value).replace(',', '.').replace(' ', '')
        # Hledáme číslo s možnou desetinnou částí
        match = re.search(r'(\d+\.?\d*)', s_val)
        return float(match.group(1)) if match else default
    except (ValueError, TypeError):
        return default

def clean_text_field(value):
    """Vyčistí textové pole od nadbytečných znaků."""
    if pd.isna(value) or value is None:
        return ''
    text = str(value).strip()
    # Odstraní nadbytečné mezery a speciální znaky
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'[\n\r\t]+', ' ', text)
    return text

def load_workstation_data():
    """Načte data o pracovištích ze souboru workstations.xlsx."""
    try:
        # Načteme mapování komponentů na pracoviště
        components_ws = pd.read_excel('workstations.xlsx', sheet_name='Components_Workstations')
        workstations_def = pd.read_excel('workstations.xlsx', sheet_name='Workstations_Definition')

        # Vytvoříme slovník pro rychlé vyhledávání
        component_to_workstation = dict(zip(components_ws['Komponent'], components_ws['Workstation']))

        # Vytvoříme slovník s definicemi pracovišť
        workstation_definitions = {}
        for _, row in workstations_def.iterrows():
            ws = row['Workstation']
            if ws not in workstation_definitions:
                workstation_definitions[ws] = {
                    'machines': [],
                    'available': [],
                    'total_price': 0
                }
            workstation_definitions[ws]['machines'].append(row['Machine'])
            workstation_definitions[ws]['available'].append(row['Available'])
            workstation_definitions[ws]['total_price'] += row['Total price'] if pd.notna(row['Total price']) else 0

        return component_to_workstation, workstation_definitions
    except Exception as e:
        print(f"Varování: Nepodařilo se načíst data o pracovištích: {e}")
        return {}, {}

def estimate_termination_complexity(component_desc, component_pn='', cross_section=None):
    """
    Odhadne složitost terminace podle nových pravidel:
    - Simple: ferrules 0.5-10mm²
    - Complex: všechny ostatní terminace 0.5-25mm² (lugs, rings, atd.)
    - Ultra Complex: všechny terminace >25mm² (hydraulický press)
    """
    if not component_desc and not component_pn:
        return 'simple'

    desc_lower = str(component_desc).lower() if component_desc else ''
    pn_lower = str(component_pn).lower() if component_pn else ''
    combined = f"{desc_lower} {pn_lower}"

    # Určíme průřez z popisu pokud není zadán
    if cross_section is None:
        cross_section = extract_cross_section_from_description(combined)

    # Ultra complex - vše nad 25mm² (hydraulický press)
    if cross_section and cross_section > 25:
        return 'ultra_complex'

    # Ferrules jsou simple do 10mm²
    ferrule_indicators = ['end sleeve', 'ferrule', 'sleeve', 'e-t-', 'e-d-', 'ahi din', 'ah din']
    is_ferrule = any(indicator in combined for indicator in ferrule_indicators)

    if is_ferrule and cross_section and cross_section <= 10:
        return 'simple'

    # Complex terminace - lugs, rings, atd. do 25mm²
    complex_indicators = ['ring', 'lug', 'crimp', 'cable lug', 'qr-t', 'rr-w', 'angle']
    is_complex = any(indicator in combined for indicator in complex_indicators)

    if is_complex and cross_section and cross_section <= 25:
        return 'complex'

    # Pokud je to ferrule nad 10mm² nebo complex nad 6mm², je to complex
    if (is_ferrule and cross_section and cross_section > 10) or \
       (is_complex and cross_section and cross_section > 6):
        return 'complex'

    # Default pro ferrules do 10mm²
    if is_ferrule:
        return 'simple'

    # Default pro ostatní do 6mm²
    return 'complex' if cross_section and cross_section > 6 else 'simple'

def extract_cross_section_from_description(description):
    """Extrahuje průřez z popisu komponenty."""
    if not description:
        return None

    # Hledáme vzory jako "2.5mm²", "6SQMM", "10mm2", atd.
    patterns = [
        r'(\d+\.?\d*)\s*mm²',
        r'(\d+\.?\d*)\s*mm2',
        r'(\d+\.?\d*)\s*sqmm',
        r'(\d+\.?\d*)\s*mm²',
        r'(\d+\.?\d*)/\d+',  # AWG formát
    ]

    for pattern in patterns:
        match = re.search(pattern, description.lower())
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                continue

    return None

def get_component_workstation(component_pn, component_to_workstation):
    """Získá pracoviště pro daný komponent."""
    if not component_pn or pd.isna(component_pn):
        return None

    # Přímé vyhledání
    workstation = component_to_workstation.get(str(component_pn))
    if workstation:
        return workstation

    # Pokud nenajdeme přímé mapování, zkusíme bez mezer a speciálních znaků
    clean_pn = re.sub(r'[^\w]', '', str(component_pn))
    for pn, ws in component_to_workstation.items():
        clean_mapped_pn = re.sub(r'[^\w]', '', str(pn))
        if clean_pn == clean_mapped_pn:
            return ws

    return None

def process_file_enhanced(filepath, component_to_workstation, workstation_definitions):
    """Rozšířené zpracování souboru s extrakcí všech relevantních dat."""
    print(f"Zpracovávám soubor: {os.path.basename(filepath)}")

    # Najdeme řádek se záhlavím
    header_row_index = find_header_row(filepath, start_column_name)
    if header_row_index is None:
        print(f"  - Chyba: V souboru nebylo nalezeno záhlaví se sloupcem '{start_column_name}'. Soubor bude přeskočen.")
        return None

    try:
        # Načteme soubor se správným záhlavím
        df = pd.read_excel(filepath, header=header_row_index)

        # Odstraníme řádky s popisky a prázdné řádky
        df = df[df[start_column_name].notna()]
        df = df[~df[start_column_name].astype(str).str.contains('Marking|No\.|TabulationList', na=False)]

        if len(df) == 0:
            print(f"  - Varování: Po vyčištění nezbyla žádná data.")
            return None

        # Vytvoříme výsledný DataFrame s mapovanými sloupci
        result_data = []

        for _, row in df.iterrows():
            record = {}

            # Základní mapování sloupců
            for source_col, target_col in COLUMN_MAPPINGS.items():
                if source_col in df.columns:
                    value = row[source_col]

                    # Speciální zpracování podle typu sloupce
                    if 'Length' in source_col or 'mm' in target_col:
                        record[target_col] = extract_numeric_value(value, 0)
                    elif 'Section' in source_col or 'CSA' in source_col:
                        record[target_col] = extract_numeric_value(value)
                    elif 'Strip' in target_col or 'Odizol' in source_col:
                        record[target_col] = extract_numeric_value(value, 0)
                    else:
                        record[target_col] = clean_text_field(value)

            # Přidáme metadata souboru
            record['Source_File'] = os.path.basename(filepath)
            record['File_Type'] = 'xlsm' if filepath.endswith('.xlsm') else 'xlsx'

            # Získáme průřez kabelu
            cross_section = record.get('Cross_Section_mm2')

            # Odhadneme složitost terminace s novými pravidly
            comp_a_pn = record.get('Component_A_PN', '')
            comp_a_desc = record.get('Component_A_Description', '')
            comp_b_pn = record.get('Component_B_PN', '')
            comp_b_desc = record.get('Component_B_Description', '')

            record['Termination_A_Complexity'] = estimate_termination_complexity(
                comp_a_desc, comp_a_pn, cross_section)
            record['Termination_B_Complexity'] = estimate_termination_complexity(
                comp_b_desc, comp_b_pn, cross_section)

            # Určíme pracoviště pro komponenty
            record['Component_A_Workstation'] = get_component_workstation(comp_a_pn, component_to_workstation)
            record['Component_B_Workstation'] = get_component_workstation(comp_b_pn, component_to_workstation)

            # Přidáme definice pracovišť
            ws_a = record['Component_A_Workstation']
            ws_b = record['Component_B_Workstation']

            if ws_a and ws_a in workstation_definitions:
                record['Workstation_A_Machines'] = ', '.join(workstation_definitions[ws_a]['machines'])
                record['Workstation_A_Available'] = any(workstation_definitions[ws_a]['available'])
            else:
                record['Workstation_A_Machines'] = ''
                record['Workstation_A_Available'] = False

            if ws_b and ws_b in workstation_definitions:
                record['Workstation_B_Machines'] = ', '.join(workstation_definitions[ws_b]['machines'])
                record['Workstation_B_Available'] = any(workstation_definitions[ws_b]['available'])
            else:
                record['Workstation_B_Machines'] = ''
                record['Workstation_B_Available'] = False

            # Vypočítáme odhady časů s novými pravidly
            cable_length = record.get('Cable_Length_mm', 0) or 0
            strip_a = record.get('Component_A_Strip_Length', 0) or 0
            strip_b = record.get('Component_B_Strip_Length', 0) or 0

            # CUTTING - SOLO ANALÝZA (kabely přicházejí nastříhané)
            cutting_time = cable_length * TIME_ESTIMATES['cutting_per_mm']

            # VÝROBNÍ ČASY NA PRACOVIŠTÍCH (bez cutting)
            stripping_time = (strip_a + strip_b) * TIME_ESTIMATES['stripping_per_mm']

            # Časy terminace podle nové logiky
            def get_termination_time(complexity):
                if complexity == 'ultra_complex':
                    return TIME_ESTIMATES['crimping_ultra_complex']
                elif complexity == 'complex':
                    return TIME_ESTIMATES['crimping_complex']
                else:
                    return TIME_ESTIMATES['crimping_simple']

            term_a_time = get_termination_time(record['Termination_A_Complexity'])
            term_b_time = get_termination_time(record['Termination_B_Complexity'])

            # Čas přípravy pracoviště (pokud se mění)
            setup_time = 0
            if ws_a:
                setup_time += TIME_ESTIMATES.get(f'setup_workstation_{ws_a[0]}', 1.0)
            if ws_b and ws_b != ws_a:
                setup_time += TIME_ESTIMATES.get(f'setup_workstation_{ws_b[0]}', 1.0)

            # CELKOVÝ ČAS NA PRACOVIŠTÍCH (bez cutting)
            workstation_time = (TIME_ESTIMATES['base_handling'] + stripping_time +
                               term_a_time + term_b_time + setup_time + TIME_ESTIMATES['quality_check'])

            # CELKOVÝ ČAS VČETNĚ CUTTING (pro kompletní analýzu)
            total_time_with_cutting = workstation_time + cutting_time

            # Uložíme časy
            record['Workstation_Production_Time_Min'] = round(workstation_time, 2)  # Čas na pracovišti
            record['Cutting_Time_Min'] = round(cutting_time, 3)  # Solo cutting
            record['Total_Time_Including_Cutting_Min'] = round(total_time_with_cutting, 2)  # Celkový čas
            record['Stripping_Time_Min'] = round(stripping_time, 2)
            record['Termination_Time_Min'] = round(term_a_time + term_b_time, 2)
            record['Setup_Time_Min'] = round(setup_time, 2)

            # Pro zpětnou kompatibilitu - hlavní čas je čas na pracovišti
            record['Estimated_Production_Time_Min'] = record['Workstation_Production_Time_Min']

            result_data.append(record)

        result_df = pd.DataFrame(result_data)

        # Vyfiltrujeme řádky s chybějícími klíčovými daty
        result_df = result_df[result_df['Cable_ID'].notna()]
        result_df = result_df[result_df['Cable_ID'].astype(str).str.strip() != '']

        print(f"  - Úspěšně zpracováno {len(result_df)} platných řádků.")
        return result_df

    except Exception as e:
        print(f"  - Chyba při zpracování souboru: {e}")
        return None


def create_production_analysis(df):
    """Vytvoří pokročilé analýzy pro taktování výroby."""
    analyses = {}

    # 1. Analýza podle bundlů
    bundle_analysis = df.groupby('Bundle_ID').agg({
        'Cable_ID': 'count',
        'Cable_Length_mm': ['sum', 'mean', 'max'],
        'Cross_Section_mm2': ['min', 'max', 'mean'],
        'Estimated_Production_Time_Min': ['sum', 'mean', 'max'],
        'Termination_A_Complexity': lambda x: (x == 'complex').sum(),
        'Termination_B_Complexity': lambda x: (x == 'complex').sum(),
    }).round(2)

    bundle_analysis.columns = ['Cable_Count', 'Total_Length_mm', 'Avg_Length_mm', 'Max_Length_mm',
                              'Min_Cross_Section', 'Max_Cross_Section', 'Avg_Cross_Section',
                              'Total_Production_Time_Min', 'Avg_Production_Time_Min', 'Max_Production_Time_Min',
                              'Complex_Terminations_A', 'Complex_Terminations_B']

    bundle_analysis['Bundle_Complexity_Score'] = (
        bundle_analysis['Complex_Terminations_A'] +
        bundle_analysis['Complex_Terminations_B'] +
        (bundle_analysis['Cable_Count'] * 0.1) +
        (bundle_analysis['Total_Length_mm'] / 1000 * 0.05)
    ).round(2)

    analyses['Bundle_Analysis'] = bundle_analysis.reset_index()

    # 2. Analýza podle průřezů
    cross_section_analysis = df.groupby('Cross_Section_mm2').agg({
        'Cable_ID': 'count',
        'Cable_Length_mm': ['sum', 'mean'],
        'Estimated_Production_Time_Min': ['sum', 'mean'],
        'Bundle_ID': 'nunique'
    }).round(2)

    cross_section_analysis.columns = ['Cable_Count', 'Total_Length_mm', 'Avg_Length_mm',
                                     'Total_Production_Time_Min', 'Avg_Production_Time_Min', 'Bundle_Count']
    analyses['Cross_Section_Analysis'] = cross_section_analysis.reset_index()

    # 3. Analýza komponentů
    component_analysis = []
    for comp_type in ['A', 'B']:
        comp_df = df[df[f'Component_{comp_type}_PN'].notna()].copy()
        if len(comp_df) > 0:
            comp_stats = comp_df.groupby(f'Component_{comp_type}_PN').agg({
                'Cable_ID': 'count',
                f'Component_{comp_type}_Description': 'first',
                f'Termination_{comp_type}_Complexity': lambda x: (x == 'complex').sum(),
                'Estimated_Production_Time_Min': 'mean'
            }).round(2)
            comp_stats.columns = ['Usage_Count', 'Description', 'Complex_Count', 'Avg_Time_Min']
            comp_stats['Component_Type'] = comp_type
            component_analysis.append(comp_stats.reset_index())

    if component_analysis:
        analyses['Component_Analysis'] = pd.concat(component_analysis, ignore_index=True)

    # 4. Analýza podle délek kabelů
    df['Length_Category'] = pd.cut(df['Cable_Length_mm'],
                                  bins=[0, 500, 1000, 2000, 5000, float('inf')],
                                  labels=['Very_Short_0-500mm', 'Short_500-1000mm', 'Medium_1000-2000mm',
                                         'Long_2000-5000mm', 'Very_Long_5000mm+'])

    length_analysis = df.groupby('Length_Category').agg({
        'Cable_ID': 'count',
        'Estimated_Production_Time_Min': ['sum', 'mean'],
        'Bundle_ID': 'nunique'
    }).round(2)

    length_analysis.columns = ['Cable_Count', 'Total_Production_Time_Min', 'Avg_Production_Time_Min', 'Bundle_Count']
    analyses['Length_Analysis'] = length_analysis.reset_index()

    return analyses

def create_summary_statistics(df):
    """Vytvoří souhrnné statistiky."""
    summary = {
        'Total_Cables': len(df),
        'Unique_Bundles': df['Bundle_ID'].nunique(),
        'Total_Cable_Length_m': round(df['Cable_Length_mm'].sum() / 1000, 2),

        # Časy na pracovištích (bez cutting)
        'Total_Workstation_Time_Hours': round(df['Workstation_Production_Time_Min'].sum() / 60, 2),
        'Avg_Workstation_Time_Per_Cable_Min': round(df['Workstation_Production_Time_Min'].mean(), 2),

        # Cutting časy (solo)
        'Total_Cutting_Time_Hours': round(df['Cutting_Time_Min'].sum() / 60, 2),
        'Avg_Cutting_Time_Per_Cable_Min': round(df['Cutting_Time_Min'].mean(), 3),

        # Celkové časy (včetně cutting)
        'Total_Time_Including_Cutting_Hours': round(df['Total_Time_Including_Cutting_Min'].sum() / 60, 2),
        'Avg_Total_Time_Per_Cable_Min': round(df['Total_Time_Including_Cutting_Min'].mean(), 2),

        # Složitost terminací
        'Complex_Terminations_Count': len(df[(df['Termination_A_Complexity'] == 'complex') |
                                           (df['Termination_B_Complexity'] == 'complex')]),
        'Ultra_Complex_Terminations_Count': len(df[(df['Termination_A_Complexity'] == 'ultra_complex') |
                                                 (df['Termination_B_Complexity'] == 'ultra_complex')]),

        # Ostatní statistiky
        'Unique_Cross_Sections': df['Cross_Section_mm2'].nunique(),
        'Cross_Section_Range': f"{df['Cross_Section_mm2'].min()}-{df['Cross_Section_mm2'].max()}mm²",
        'Unique_Components_A': df['Component_A_PN'].nunique(),
        'Unique_Components_B': df['Component_B_PN'].nunique(),
        'Mapped_Components_To_Workstations': df['Component_A_Workstation'].notna().sum() + df['Component_B_Workstation'].notna().sum(),
    }
    return pd.DataFrame([summary])

def create_production_recommendations(df):
    """Vytvoří doporučení pro optimalizaci výroby."""
    # Analýza bundlů podle složitosti
    bundle_analysis = df.groupby('Bundle_ID').agg({
        'Cable_ID': 'count',
        'Estimated_Production_Time_Min': 'sum',
        'Cross_Section_mm2': ['min', 'max'],
        'Cable_Length_mm': 'sum'
    }).round(2)

    bundle_analysis.columns = ['Cable_Count', 'Total_Time_Min', 'Min_Cross_Section', 'Max_Cross_Section', 'Total_Length_mm']
    bundle_analysis['Avg_Time_Per_Cable'] = (bundle_analysis['Total_Time_Min'] / bundle_analysis['Cable_Count']).round(2)

    # Kategorizace bundlů
    bundle_analysis['Production_Category'] = 'Standard'
    bundle_analysis.loc[bundle_analysis['Total_Time_Min'] > 1000, 'Production_Category'] = 'High_Volume'
    bundle_analysis.loc[bundle_analysis['Avg_Time_Per_Cable'] > 8, 'Production_Category'] = 'Complex'
    bundle_analysis.loc[(bundle_analysis['Total_Time_Min'] > 1000) &
                       (bundle_analysis['Avg_Time_Per_Cable'] > 8), 'Production_Category'] = 'High_Volume_Complex'

    # Doporučení pro sekvencování
    bundle_analysis['Recommended_Batch_Size'] = bundle_analysis['Cable_Count'].apply(
        lambda x: min(50, max(10, x // 4)) if x > 20 else x
    )

    bundle_analysis['Priority_Score'] = (
        bundle_analysis['Total_Time_Min'] * 0.4 +
        bundle_analysis['Cable_Count'] * 0.3 +
        bundle_analysis['Avg_Time_Per_Cable'] * 0.3
    ).round(2)

    return bundle_analysis.reset_index()

def create_workstation_analysis(df):
    """Vytvoří analýzu pro rozdělení práce mezi pracoviště."""
    workstation_data = []

    # Rozdělení podle složitosti terminací s novými kategoriemi
    simple_cables = df[(df['Termination_A_Complexity'] == 'simple') &
                      (df['Termination_B_Complexity'] == 'simple')]
    complex_cables = df[((df['Termination_A_Complexity'] == 'complex') |
                        (df['Termination_B_Complexity'] == 'complex')) &
                       (df['Termination_A_Complexity'] != 'ultra_complex') &
                       (df['Termination_B_Complexity'] != 'ultra_complex')]
    ultra_complex_cables = df[(df['Termination_A_Complexity'] == 'ultra_complex') |
                             (df['Termination_B_Complexity'] == 'ultra_complex')]

    # Simple terminace (ferrules 0.5-10mm²)
    if len(simple_cables) > 0:
        workstation_data.append({
            'Workstation_Type': 'Simple_Termination_Ferrules',
            'Cable_Count': len(simple_cables),
            'Total_Time_Hours': round(simple_cables['Estimated_Production_Time_Min'].sum() / 60, 2),
            'Avg_Time_Per_Cable_Min': round(simple_cables['Estimated_Production_Time_Min'].mean(), 2),
            'Cross_Section_Range': f"{simple_cables['Cross_Section_mm2'].min()}-{simple_cables['Cross_Section_mm2'].max()}mm²",
            'Recommended_Batch_Size': '30-60 cables',
            'Equipment': 'Standard crimping tools'
        })

    # Complex terminace (lugs, rings 0.5-25mm²)
    if len(complex_cables) > 0:
        workstation_data.append({
            'Workstation_Type': 'Complex_Termination_Lugs_Rings',
            'Cable_Count': len(complex_cables),
            'Total_Time_Hours': round(complex_cables['Estimated_Production_Time_Min'].sum() / 60, 2),
            'Avg_Time_Per_Cable_Min': round(complex_cables['Estimated_Production_Time_Min'].mean(), 2),
            'Cross_Section_Range': f"{complex_cables['Cross_Section_mm2'].min()}-{complex_cables['Cross_Section_mm2'].max()}mm²",
            'Recommended_Batch_Size': '15-30 cables',
            'Equipment': 'Advanced crimping tools'
        })

    # Ultra complex terminace (hydraulický press >25mm²)
    if len(ultra_complex_cables) > 0:
        workstation_data.append({
            'Workstation_Type': 'Ultra_Complex_Hydraulic_Press',
            'Cable_Count': len(ultra_complex_cables),
            'Total_Time_Hours': round(ultra_complex_cables['Estimated_Production_Time_Min'].sum() / 60, 2),
            'Avg_Time_Per_Cable_Min': round(ultra_complex_cables['Estimated_Production_Time_Min'].mean(), 2),
            'Cross_Section_Range': f"{ultra_complex_cables['Cross_Section_mm2'].min()}-{ultra_complex_cables['Cross_Section_mm2'].max()}mm²",
            'Recommended_Batch_Size': '5-15 cables',
            'Equipment': 'Hydraulic press'
        })

    # Analýza podle skutečných pracovišť
    workstation_columns = ['Component_A_Workstation', 'Component_B_Workstation']
    for col in workstation_columns:
        if col in df.columns:
            workstation_stats = df[df[col].notna()].groupby(col).agg({
                'Cable_ID': 'count',
                'Estimated_Production_Time_Min': ['sum', 'mean'],
                'Cross_Section_mm2': ['min', 'max'],
                'Termination_A_Complexity': lambda x: (x == 'ultra_complex').sum(),
                'Termination_B_Complexity': lambda x: (x == 'ultra_complex').sum()
            }).round(2)

            for ws in workstation_stats.index:
                stats = workstation_stats.loc[ws]
                workstation_data.append({
                    'Workstation_Type': f'Actual_Workstation_{ws}_{col.split("_")[1]}',
                    'Cable_Count': int(stats[('Cable_ID', 'count')]),
                    'Total_Time_Hours': round(stats[('Estimated_Production_Time_Min', 'sum')] / 60, 2),
                    'Avg_Time_Per_Cable_Min': round(stats[('Estimated_Production_Time_Min', 'mean')], 2),
                    'Cross_Section_Range': f"{stats[('Cross_Section_mm2', 'min')]}-{stats[('Cross_Section_mm2', 'max')]}mm²",
                    'Ultra_Complex_Count': int(stats[('Termination_A_Complexity', '<lambda>')] + stats[('Termination_B_Complexity', '<lambda>')]),
                    'Equipment': f'Workstation {ws} equipment'
                })

    return pd.DataFrame(workstation_data)

def create_cutting_analysis(df):
    """Vytvoří samostatnou analýzu cutting operací."""
    cutting_data = []

    # Celková statistika cutting
    total_cutting_time = df['Cutting_Time_Min'].sum()
    total_cable_length = df['Cable_Length_mm'].sum()
    avg_cutting_time = df['Cutting_Time_Min'].mean()

    cutting_data.append({
        'Analysis_Type': 'Total_Cutting_Operations',
        'Cable_Count': len(df),
        'Total_Cable_Length_m': round(total_cable_length / 1000, 2),
        'Total_Cutting_Time_Hours': round(total_cutting_time / 60, 2),
        'Avg_Cutting_Time_Per_Cable_Min': round(avg_cutting_time, 3),
        'Cutting_Rate_m_per_hour': round((total_cable_length / 1000) / (total_cutting_time / 60), 1) if total_cutting_time > 0 else 0,
        'Recommended_Daily_Capacity_m': round((8 * 60 * 0.85) * TIME_ESTIMATES['cutting_per_mm'] * 1000, 0)
    })

    # Analýza podle délek kabelů
    df['Length_Category_Cutting'] = pd.cut(df['Cable_Length_mm'],
                                          bins=[0, 300, 600, 1000, 2000, float('inf')],
                                          labels=['Very_Short_0-300mm', 'Short_300-600mm', 'Medium_600-1000mm',
                                                 'Long_1000-2000mm', 'Very_Long_2000mm+'])

    length_cutting_analysis = df.groupby('Length_Category_Cutting').agg({
        'Cable_ID': 'count',
        'Cable_Length_mm': ['sum', 'mean'],
        'Cutting_Time_Min': ['sum', 'mean']
    }).round(3)

    length_cutting_analysis.columns = ['Cable_Count', 'Total_Length_mm', 'Avg_Length_mm',
                                      'Total_Cutting_Time_Min', 'Avg_Cutting_Time_Min']

    for category, data in length_cutting_analysis.iterrows():
        cutting_data.append({
            'Analysis_Type': f'Cutting_{category}',
            'Cable_Count': int(data['Cable_Count']),
            'Total_Cable_Length_m': round(data['Total_Length_mm'] / 1000, 2),
            'Total_Cutting_Time_Hours': round(data['Total_Cutting_Time_Min'] / 60, 2),
            'Avg_Cutting_Time_Per_Cable_Min': round(data['Avg_Cutting_Time_Min'], 3),
            'Cutting_Rate_m_per_hour': round((data['Total_Length_mm'] / 1000) / (data['Total_Cutting_Time_Min'] / 60), 1) if data['Total_Cutting_Time_Min'] > 0 else 0,
            'Recommended_Batch_Size': '100-200 cables' if 'Very_Short' in category else '80-150 cables' if 'Short' in category else '60-120 cables' if 'Medium' in category else '40-80 cables' if 'Long' in category else '20-50 cables'
        })

    # Analýza podle bundlů pro cutting
    bundle_cutting_analysis = df.groupby('Bundle_ID').agg({
        'Cable_ID': 'count',
        'Cable_Length_mm': 'sum',
        'Cutting_Time_Min': 'sum'
    }).round(3)

    bundle_cutting_analysis['Cutting_Efficiency_Score'] = (
        bundle_cutting_analysis['Cable_ID'] * 0.4 +
        (bundle_cutting_analysis['Cable_Length_mm'] / 1000) * 0.6
    ).round(2)

    # Top 10 bundlů pro cutting
    top_cutting_bundles = bundle_cutting_analysis.nlargest(10, 'Cutting_Time_Min')

    for bundle_id, data in top_cutting_bundles.iterrows():
        cutting_data.append({
            'Analysis_Type': f'Bundle_Cutting_{bundle_id}',
            'Cable_Count': int(data['Cable_ID']),
            'Total_Cable_Length_m': round(data['Cable_Length_mm'] / 1000, 2),
            'Total_Cutting_Time_Hours': round(data['Cutting_Time_Min'] / 60, 2),
            'Avg_Cutting_Time_Per_Cable_Min': round(data['Cutting_Time_Min'] / data['Cable_ID'], 3),
            'Cutting_Rate_m_per_hour': round((data['Cable_Length_mm'] / 1000) / (data['Cutting_Time_Min'] / 60), 1) if data['Cutting_Time_Min'] > 0 else 0,
            'Cutting_Efficiency_Score': data['Cutting_Efficiency_Score']
        })

    return pd.DataFrame(cutting_data)

def create_takt_time_analysis(df):
    """Vytvoří analýzu taktovacích časů pro optimalizaci výroby."""
    # Analýza podle bundlů s doporučeními pro taktování
    bundle_takt = df.groupby('Bundle_ID').agg({
        'Cable_ID': 'count',
        'Estimated_Production_Time_Min': ['sum', 'mean', 'std'],
        'Cross_Section_mm2': ['min', 'max'],
        'Cable_Length_mm': ['sum', 'mean'],
        'Termination_A_Complexity': lambda x: (x == 'complex').sum(),
        'Termination_B_Complexity': lambda x: (x == 'complex').sum()
    }).round(2)

    bundle_takt.columns = ['Cable_Count', 'Total_Time_Min', 'Avg_Time_Min', 'Time_Std_Dev',
                          'Min_Cross_Section', 'Max_Cross_Section', 'Total_Length_mm', 'Avg_Length_mm',
                          'Complex_Term_A', 'Complex_Term_B']

    # Výpočet doporučených taktovacích časů
    bundle_takt['Recommended_Takt_Time_Min'] = bundle_takt['Avg_Time_Min'] * 1.2  # 20% buffer
    bundle_takt['Setup_Time_Min'] = bundle_takt['Cable_Count'] * 0.5  # Odhad času přípravy
    bundle_takt['Total_Bundle_Time_Min'] = bundle_takt['Total_Time_Min'] + bundle_takt['Setup_Time_Min']

    # Kategorizace podle složitosti
    bundle_takt['Complexity_Level'] = 'Low'
    bundle_takt.loc[(bundle_takt['Complex_Term_A'] + bundle_takt['Complex_Term_B']) > 5, 'Complexity_Level'] = 'Medium'
    bundle_takt.loc[(bundle_takt['Complex_Term_A'] + bundle_takt['Complex_Term_B']) > 15, 'Complexity_Level'] = 'High'

    # Doporučení pro velikost dávky
    bundle_takt['Recommended_Batch_Size'] = bundle_takt.apply(
        lambda row: min(25, max(5, row['Cable_Count'] // 3)) if row['Complexity_Level'] == 'High'
        else min(40, max(10, row['Cable_Count'] // 2)) if row['Complexity_Level'] == 'Medium'
        else min(60, max(15, row['Cable_Count'])), axis=1
    )

    # Odhad počtu dávek
    bundle_takt['Estimated_Batches'] = np.ceil(bundle_takt['Cable_Count'] / bundle_takt['Recommended_Batch_Size'])
    bundle_takt['Time_Per_Batch_Min'] = (bundle_takt['Total_Bundle_Time_Min'] / bundle_takt['Estimated_Batches']).round(2)

    return bundle_takt.reset_index()

def create_daily_production_plan(df, working_hours_per_day=8, efficiency_factor=0.85):
    """Vytvoří plán denní výroby."""
    available_minutes_per_day = working_hours_per_day * 60 * efficiency_factor

    # Seřadíme bundly podle priority (složitost + objem)
    bundle_priority = df.groupby('Bundle_ID').agg({
        'Cable_ID': 'count',
        'Estimated_Production_Time_Min': 'sum',
        'Cross_Section_mm2': 'mean'
    }).round(2)

    bundle_priority['Priority_Score'] = (
        bundle_priority['Estimated_Production_Time_Min'] * 0.5 +
        bundle_priority['Cable_ID'] * 0.3 +
        bundle_priority['Cross_Section_mm2'] * 0.2
    )

    bundle_priority = bundle_priority.sort_values('Priority_Score', ascending=False)
    bundle_priority['Cumulative_Time_Min'] = bundle_priority['Estimated_Production_Time_Min'].cumsum()
    bundle_priority['Production_Day'] = np.ceil(bundle_priority['Cumulative_Time_Min'] / available_minutes_per_day)

    # Vytvoříme denní plán
    daily_plan = []
    for day in range(1, int(bundle_priority['Production_Day'].max()) + 1):
        day_bundles = bundle_priority[bundle_priority['Production_Day'] == day]
        daily_plan.append({
            'Production_Day': day,
            'Bundle_Count': len(day_bundles),
            'Total_Cables': day_bundles['Cable_ID'].sum(),
            'Total_Time_Min': day_bundles['Estimated_Production_Time_Min'].sum(),
            'Total_Time_Hours': round(day_bundles['Estimated_Production_Time_Min'].sum() / 60, 2),
            'Utilization_Percent': round((day_bundles['Estimated_Production_Time_Min'].sum() / available_minutes_per_day) * 100, 1),
            'Bundle_IDs': ', '.join(map(str, day_bundles.index.tolist()))
        })

    return pd.DataFrame(daily_plan), bundle_priority.reset_index()

def main():
    """Hlavní funkce pro spuštění rozšířeného procesu analýzy."""
    print("=== ROZŠÍŘENÁ ANALÝZA SETŮ CTEC PRO TAKTOVÁNÍ VÝROBY V2.0 ===")
    print(f"Čas spuštění: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Načteme data o pracovištích
    print("Načítám data o pracovištích...")
    component_to_workstation, workstation_definitions = load_workstation_data()
    if component_to_workstation:
        print(f"  - Načteno {len(component_to_workstation)} mapování komponentů na pracoviště")
        print(f"  - Načteno {len(workstation_definitions)} definic pracovišť")
    else:
        print("  - Varování: Data o pracovištích nebyla načtena, bude použita základní logika")
    print()

    # Najdeme všechny Excel soubory (kromě workstations.xlsx)
    excel_files = glob.glob(os.path.join(cesta_k_souborum, "*.xlsx"))
    xlsm_files = glob.glob(os.path.join(cesta_k_souborum, "*.xlsm"))
    vsechny_soubory = excel_files + xlsm_files

    # Odfiltrujeme soubory, které nechceme zpracovávat
    excluded_files = ['workstations.xlsx', vystupni_soubor_excel]
    vsechny_soubory = [f for f in vsechny_soubory if os.path.basename(f) not in excluded_files]

    if not vsechny_soubory:
        print(f"Chyba: Ve složce '{cesta_k_souborum}' nebyly nalezeny žádné Excel soubory k zpracování.")
        return

    print(f"Nalezeno {len(vsechny_soubory)} souborů k zpracování:")
    for soubor in vsechny_soubory:
        print(f"  - {os.path.basename(soubor)}")
    print()

    # Zpracujeme všechny soubory
    all_data_frames = []
    for soubor in vsechny_soubory:
        processed_df = process_file_enhanced(soubor, component_to_workstation, workstation_definitions)
        if processed_df is not None:
            all_data_frames.append(processed_df)

    if not all_data_frames:
        print("Nebyly zpracovány žádné platné soubory. Skript končí.")
        return

    # Spojíme všechna data
    print("Spojuji data ze všech souborů...")
    kompletni_data = pd.concat(all_data_frames, ignore_index=True)
    print(f"Celkem spojeno {len(kompletni_data)} řádků ze všech souborů.")
    print()

    # Vytvoříme analýzy
    print("Vytvářím pokročilé analýzy...")
    analyses = create_production_analysis(kompletni_data)
    summary_stats = create_summary_statistics(kompletni_data)
    production_recommendations = create_production_recommendations(kompletni_data)
    workstation_analysis = create_workstation_analysis(kompletni_data)
    cutting_analysis = create_cutting_analysis(kompletni_data)  # NOVÁ SOLO CUTTING ANALÝZA
    takt_time_analysis = create_takt_time_analysis(kompletni_data)
    daily_plan, bundle_priority = create_daily_production_plan(kompletni_data)

    # Export do Excel souboru s více listy
    print("Exportuji výsledky...")
    try:
        with pd.ExcelWriter(vystupni_soubor_excel, engine='openpyxl') as writer:
            # Hlavní data
            kompletni_data.to_excel(writer, sheet_name='Complete_Data', index=False)

            # Souhrnné statistiky
            summary_stats.to_excel(writer, sheet_name='Summary_Statistics', index=False)

            # Doporučení pro výrobu
            production_recommendations.to_excel(writer, sheet_name='Production_Recommendations', index=False)

            # Analýza pracovišť
            workstation_analysis.to_excel(writer, sheet_name='Workstation_Analysis', index=False)

            # SOLO Cutting analýza
            cutting_analysis.to_excel(writer, sheet_name='Cutting_Analysis_SOLO', index=False)

            # Taktovací analýza
            takt_time_analysis.to_excel(writer, sheet_name='Takt_Time_Analysis', index=False)

            # Denní plán výroby
            daily_plan.to_excel(writer, sheet_name='Daily_Production_Plan', index=False)

            # Priorita bundlů
            bundle_priority.to_excel(writer, sheet_name='Bundle_Priority', index=False)

            # Jednotlivé analýzy
            for analysis_name, analysis_df in analyses.items():
                sheet_name = analysis_name[:31]  # Excel limit pro název listu
                analysis_df.to_excel(writer, sheet_name=sheet_name, index=False)

        print(f"✓ Excel soubor uložen: {os.path.abspath(vystupni_soubor_excel)}")

        # Export do CSV pro snadné další zpracování
        kompletni_data.to_csv(vystupni_soubor_csv, index=False, encoding='utf-8-sig')
        print(f"✓ CSV soubor uložen: {os.path.abspath(vystupni_soubor_csv)}")

        # Výpis základních statistik
        print("\n=== SOUHRNNÉ STATISTIKY ===")
        for key, value in summary_stats.iloc[0].items():
            print(f"{key.replace('_', ' ')}: {value}")

        # Výpis statistik složitosti
        print("\n=== STATISTIKY SLOŽITOSTI TERMINACÍ ===")
        complexity_stats = kompletni_data.groupby(['Termination_A_Complexity', 'Termination_B_Complexity']).size()
        for (comp_a, comp_b), count in complexity_stats.items():
            print(f"{comp_a} + {comp_b}: {count} kabelů")

        # Výpis SOLO cutting statistik
        print("\n=== SOLO CUTTING ANALÝZA ===")
        total_cutting_time = kompletni_data['Cutting_Time_Min'].sum()
        total_cable_length = kompletni_data['Cable_Length_mm'].sum()
        print(f"Celkový čas cutting: {total_cutting_time/60:.1f} hodin")
        print(f"Celková délka kabelů: {total_cable_length/1000:.1f} metrů")
        print(f"Rychlost cutting: {(total_cable_length/1000)/(total_cutting_time/60):.1f} m/hodinu")
        print(f"Průměrný čas cutting na kabel: {kompletni_data['Cutting_Time_Min'].mean():.3f} minut")

        # Výpis statistik pracovišť
        if 'Component_A_Workstation' in kompletni_data.columns:
            print("\n=== STATISTIKY PRACOVIŠŤ (BEZ CUTTING) ===")
            ws_stats = kompletni_data['Component_A_Workstation'].value_counts()
            for ws, count in ws_stats.head(10).items():
                if pd.notna(ws):
                    print(f"Pracoviště {ws}: {count} komponentů")

            print(f"\nPrůměrný čas na pracovišti (bez cutting): {kompletni_data['Workstation_Production_Time_Min'].mean():.2f} minut/kabel")
            print(f"Celkový čas na pracovištích: {kompletni_data['Workstation_Production_Time_Min'].sum()/60:.1f} hodin")

    except Exception as e:
        print(f"Chyba při ukládání souborů: {e}")

if __name__ == "__main__":
    main()